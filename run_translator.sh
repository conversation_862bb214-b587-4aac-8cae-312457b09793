#!/bin/bash

# 翻译工具启动脚本

echo "启动翻译工具..."

# 检查 Python 环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到 Python3"
    exit 1
fi

# 检查依赖
echo "检查依赖..."
python3 -c "import sys; sys.path.insert(0, 'src'); from translator import *" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "依赖检查失败，正在安装..."
    pip3 install -r requirements.txt
fi

# 运行测试
echo "运行测试..."
python3 test_translator.py

if [ $? -eq 0 ]; then
    echo "测试通过，启动应用程序..."
    python3 src/translator.py
else
    echo "测试失败，请检查配置"
    exit 1
fi
