#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
翻译工具测试脚本
用于测试各项功能是否正常工作
"""

import sys
import os
import time
import subprocess
import requests

# 添加 src 目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

VLLM_API_URL = "http://10.164.110.54:8000/v1"

def test_vllm_connection():
    """测试 vLLM API 连接"""
    print("测试 vLLM API 连接...")
    try:
        headers = {
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "Qwen/Qwen-7B-Chat",
            "messages": [
                {
                    "role": "user",
                    "content": "Hello, this is a test message."
                }
            ],
            "temperature": 0.7,
            "max_tokens": 100
        }
        
        response = requests.post(
            f"{VLLM_API_URL}/chat/completions",
            headers=headers,
            json=data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ vLLM API 连接成功")
            print(f"响应: {result['choices'][0]['message']['content'][:100]}...")
            return True
        else:
            print(f"❌ vLLM API 连接失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ vLLM API 连接出错: {str(e)}")
        return False

def test_dependencies():
    """测试依赖包"""
    print("测试依赖包...")
    
    required_packages = [
        'pyobjc-framework-Cocoa',
        'pyobjc-framework-Quartz',
        'pyperclip',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'pyobjc-framework-Cocoa':
                from Foundation import NSObject
                from AppKit import NSApplication
            elif package == 'pyobjc-framework-Quartz':
                from Quartz import CGEventCreateKeyboardEvent
            elif package == 'pyperclip':
                import pyperclip
            elif package == 'requests':
                import requests
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True

def test_accessibility_permissions():
    """测试辅助功能权限"""
    print("测试辅助功能权限...")
    try:
        from Quartz import AXIsProcessTrusted
        if AXIsProcessTrusted():
            print("✅ 已获得辅助功能权限")
            return True
        else:
            print("❌ 未获得辅助功能权限")
            print("请在系统偏好设置 > 安全性与隐私 > 隐私 > 辅助功能中添加此应用程序")
            return False
    except Exception as e:
        print(f"❌ 检查辅助功能权限失败: {str(e)}")
        return False

def test_app_launch():
    """测试应用程序启动"""
    print("测试应用程序启动...")
    try:
        # 尝试导入主模块
        from translator import TranslatorApp, check_accessibility_permissions
        print("✅ 主模块导入成功")
        
        # 创建应用实例
        translator = TranslatorApp.alloc().init()
        print("✅ 应用实例创建成功")
        
        return True
    except Exception as e:
        print(f"❌ 应用程序启动测试失败: {str(e)}")
        return False

def run_manual_test():
    """运行手动测试"""
    print("\n" + "="*50)
    print("手动测试指南")
    print("="*50)
    print("1. 运行应用程序: python src/translator.py")
    print("2. 检查状态栏是否出现 '译' 图标")
    print("3. 点击图标，检查菜单是否包含:")
    print("   - 翻译选中文本 (⌘T)")
    print("   - 注释选中代码 (⌘C)")
    print("   - 解释选中内容 (⌘E)")
    print("   - 设置")
    print("   - 退出 (⌘Q)")
    print("4. 在任意应用中选中文本，然后使用菜单功能")
    print("5. 检查翻译结果窗口是否正常显示")

def main():
    """主测试函数"""
    print("翻译工具测试开始")
    print("="*50)
    
    tests = [
        ("依赖包检查", test_dependencies),
        ("vLLM API 连接", test_vllm_connection),
        ("辅助功能权限", test_accessibility_permissions),
        ("应用程序启动", test_app_launch),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1
    
    print("\n" + "="*50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✅ 所有测试通过！")
        run_manual_test()
    else:
        print("❌ 部分测试失败，请检查上述问题")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
