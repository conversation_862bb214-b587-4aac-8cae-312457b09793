#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
可工作的翻译工具 - 避免 Objective-C 方法定义问题
"""

import sys
import time
import threading
import requests
import pyperclip
import objc
from Foundation import *
from AppKit import *
from PyObjCTools import AppHelper

VLLM_API_URL = "http://*************:8000/v1"

class WorkingTranslatorApp(NSObject):
    def init(self):
        self = objc.super(WorkingTranslatorApp, self).init()
        if self is None:
            return None

        print("初始化翻译工具...")

        try:
            # 创建状态栏
            print("正在创建状态栏...")
            self.statusbar = NSStatusBar.systemStatusBar()
            print(f"状态栏对象: {self.statusbar}")

            self.statusItem = self.statusbar.statusItemWithLength_(NSVariableStatusItemLength)
            print(f"状态栏项: {self.statusItem}")

            if self.statusItem is None:
                print("❌ 状态栏项创建失败")
                return None

            # 设置状态栏标题 - 使用更明显的文字
            self.statusItem.setTitle_("🌐译")
            print("✅ 状态栏标题设置完成")

            # 设置工具提示
            if hasattr(self.statusItem, 'setToolTip_'):
                self.statusItem.setToolTip_("翻译工具 - 点击使用")
                print("✅ 工具提示设置完成")

        except Exception as e:
            print(f"❌ 状态栏创建失败: {str(e)}")
            return None

        try:
            # 创建菜单
            print("正在创建菜单...")
            self.menu = NSMenu.alloc().init()

            # 添加翻译菜单项
            translateItem = NSMenuItem.alloc().init()
            translateItem.setTitle_("翻译剪贴板文本")
            translateItem.setTarget_(self)
            translateItem.setAction_("translateAction:")
            self.menu.addItem_(translateItem)
            print("✅ 翻译菜单项添加完成")

            # 添加注释菜单项
            commentItem = NSMenuItem.alloc().init()
            commentItem.setTitle_("注释剪贴板代码")
            commentItem.setTarget_(self)
            commentItem.setAction_("commentAction:")
            self.menu.addItem_(commentItem)
            print("✅ 注释菜单项添加完成")

            # 添加分隔线
            self.menu.addItem_(NSMenuItem.separatorItem())

            # 添加退出菜单项
            quitItem = NSMenuItem.alloc().init()
            quitItem.setTitle_("退出")
            quitItem.setTarget_(NSApp)
            quitItem.setAction_("terminate:")
            self.menu.addItem_(quitItem)
            print("✅ 退出菜单项添加完成")

            # 设置菜单
            self.statusItem.setMenu_(self.menu)
            print("✅ 菜单设置完成")

        except Exception as e:
            print(f"❌ 菜单创建失败: {str(e)}")
            return None

        print("翻译工具初始化完成")
        return self

    def translateAction_(self, sender):
        """翻译剪贴板中的文本"""
        print("开始翻译剪贴板文本...")
        text = pyperclip.paste()
        if not text.strip():
            self.showAlert_("请先复制要翻译的文本到剪贴板")
            return

        # 在后台线程中执行翻译
        def translate_worker():
            self.translate_text(text)

        threading.Thread(target=translate_worker, daemon=True).start()

    def commentAction_(self, sender):
        """为剪贴板中的代码添加注释"""
        print("开始注释剪贴板代码...")
        text = pyperclip.paste()
        if not text.strip():
            self.showAlert_("请先复制要注释的代码到剪贴板")
            return

        # 在后台线程中执行注释
        def comment_worker():
            self.comment_text(text)

        threading.Thread(target=comment_worker, daemon=True).start()

    def translate_text(self, text):
        """执行翻译（普通 Python 方法）"""
        try:
            print(f"翻译文本: {text[:50]}...")

            # 构建API请求
            headers = {"Content-Type": "application/json"}
            data = {
                "model": "qwen",
                "messages": [
                    {
                        "role": "user",
                        "content": f"请将以下文本翻译成中文，如果原文是中文则翻译成英文：\n{text}"
                    }
                ],
                "temperature": 0.7,
                "max_tokens": 1000
            }

            # 发送请求
            response = requests.post(
                f"{VLLM_API_URL}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                translation = result['choices'][0]['message']['content'].strip()

                # 将结果复制到剪贴板
                result_text = f"原文:\n{text}\n\n翻译:\n{translation}"
                pyperclip.copy(result_text)

                # 在主线程中显示结果
                def show_result():
                    self.showAlert_(f"翻译完成！结果已复制到剪贴板\n\n{translation[:100]}...")

                NSOperationQueue.mainQueue().addOperationWithBlock_(show_result)

            else:
                def show_error():
                    self.showAlert_(f"翻译失败: HTTP {response.status_code}")
                NSOperationQueue.mainQueue().addOperationWithBlock_(show_error)

        except Exception as e:
            print(f"翻译出错: {str(e)}")
            def show_error():
                self.showAlert_(f"翻译出错: {str(e)}")
            NSOperationQueue.mainQueue().addOperationWithBlock_(show_error)

    def comment_text(self, text):
        """执行代码注释（普通 Python 方法）"""
        try:
            print(f"注释代码: {text[:50]}...")

            # 构建API请求
            headers = {"Content-Type": "application/json"}
            data = {
                "model": "qwen",
                "messages": [
                    {
                        "role": "user",
                        "content": f"请为以下代码添加详细的中文注释，保持原有代码结构不变：\n\n{text}"
                    }
                ],
                "temperature": 0.7,
                "max_tokens": 1000
            }

            # 发送请求
            response = requests.post(
                f"{VLLM_API_URL}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                commented_code = result['choices'][0]['message']['content'].strip()

                # 将结果复制到剪贴板
                pyperclip.copy(commented_code)

                # 在主线程中显示结果
                def show_result():
                    self.showAlert_("代码注释完成！结果已复制到剪贴板")

                NSOperationQueue.mainQueue().addOperationWithBlock_(show_result)

            else:
                def show_error():
                    self.showAlert_(f"注释失败: HTTP {response.status_code}")
                NSOperationQueue.mainQueue().addOperationWithBlock_(show_error)

        except Exception as e:
            print(f"注释出错: {str(e)}")
            def show_error():
                self.showAlert_(f"注释出错: {str(e)}")
            NSOperationQueue.mainQueue().addOperationWithBlock_(show_error)

    def showAlert_(self, message):
        """显示提示框"""
        alert = NSAlert.alloc().init()
        alert.setMessageText_("翻译工具")
        alert.setInformativeText_(message)
        alert.setAlertStyle_(NSInformationalAlertStyle)
        alert.addButtonWithTitle_("确定")
        alert.runModal()

def main():
    """主程序入口"""
    print("启动翻译工具...")

    # 创建应用程序
    app = NSApplication.sharedApplication()
    app.setActivationPolicy_(NSApplicationActivationPolicyAccessory)

    # 创建翻译器实例
    translator = WorkingTranslatorApp.alloc().init()

    # 设置应用程序代理
    app.setDelegate_(translator)

    try:
        print("开始运行应用程序...")
        # 运行应用程序
        AppHelper.runEventLoop()
    except KeyboardInterrupt:
        print("收到中断信号，正在退出...")
        app.terminate_(None)
    except Exception as e:
        print(f"应用程序运行出错: {str(e)}")

if __name__ == "__main__":
    main()
