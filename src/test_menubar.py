#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试菜单栏图标的最简版本
"""

import objc
from Foundation import *
from AppKit import *
from PyObjCTools import AppHelper

class TestMenuBarApp(NSObject):
    def init(self):
        self = objc.super(TestMenuBarApp, self).init()
        if self is None:
            return None
        
        print("=== 开始创建菜单栏图标 ===")
        
        # 创建状态栏项
        statusBar = NSStatusBar.systemStatusBar()
        print(f"状态栏对象: {statusBar}")
        
        # 使用固定长度
        self.statusItem = statusBar.statusItemWithLength_(30)
        print(f"状态栏项对象: {self.statusItem}")
        
        if self.statusItem is None:
            print("❌ 状态栏项创建失败")
            return None
        
        # 设置标题
        self.statusItem.setTitle_("TEST")
        print("✅ 设置标题为 'TEST'")
        
        # 创建简单菜单
        menu = NSMenu.alloc().init()
        
        # 添加一个测试菜单项
        testItem = NSMenuItem.alloc().init()
        testItem.setTitle_("测试菜单")
        testItem.setTarget_(self)
        testItem.setAction_("testAction:")
        menu.addItem_(testItem)
        
        # 添加退出菜单项
        quitItem = NSMenuItem.alloc().init()
        quitItem.setTitle_("退出")
        quitItem.setTarget_(NSApp)
        quitItem.setAction_("terminate:")
        menu.addItem_(quitItem)
        
        # 设置菜单
        self.statusItem.setMenu_(menu)
        print("✅ 菜单设置完成")
        
        print("=== 菜单栏图标创建完成 ===")
        print("请检查菜单栏右侧是否有 'TEST' 图标")
        
        return self
    
    def testAction_(self, sender):
        print("测试菜单被点击了！")
        alert = NSAlert.alloc().init()
        alert.setMessageText_("测试成功！")
        alert.setInformativeText_("菜单栏图标工作正常")
        alert.addButtonWithTitle_("确定")
        alert.runModal()

def main():
    print("启动菜单栏测试...")
    
    # 创建应用程序
    app = NSApplication.sharedApplication()
    
    # 设置为辅助应用程序（不在 Dock 中显示）
    app.setActivationPolicy_(NSApplicationActivationPolicyAccessory)
    print("✅ 设置为辅助应用程序")
    
    # 创建测试应用
    testApp = TestMenuBarApp.alloc().init()
    
    if testApp is None:
        print("❌ 应用创建失败")
        return
    
    # 设置应用代理
    app.setDelegate_(testApp)
    
    print("开始运行事件循环...")
    print("按 Ctrl+C 退出")
    
    try:
        AppHelper.runEventLoop()
    except KeyboardInterrupt:
        print("收到退出信号")
        app.terminate_(None)

if __name__ == "__main__":
    main()
