#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from Foundation import *
from AppKit import *
from PyObjCTools import AppHelper
import subprocess
import pyperclip
import json
import os
import requests
import objc
import time
import threading
import traceback

VLLM_API_URL = "http://*************:8000/v1"

class TranslationService(NSObject):
    def translateService_userData_error_(self, pboard, userData, error):
        # 从剪贴板获取选中的文本
        text = pboard.stringForType_(NSStringPboardType)
        if not text:
            return False
            
        # 调用翻译功能
        translator = TranslatorApp.sharedTranslator()
        translator.translateText_(text)
        return True

class TranslationPanel(NSPanel):
    def init(self):
        self = objc.super(TranslationPanel, self).init()
        if self is None: return None
        
        # 设置窗口样式
        self.setStyleMask_(NSTitledWindowMask | NSClosableWindowMask | NSResizableWindowMask)
        self.setFloatingPanel_(True)
        self.setBecomesKeyOnlyIfNeeded_(True)
        self.setLevel_(NSFloatingWindowLevel)
        
        # 设置窗口大小和位置
        self.setFrame_(((0, 0), (400, 300)))
        
        # 创建UI
        self.setupUI()
        
        return self
    
    def setupUI(self):
        # 创建主视图
        mainView = NSView.alloc().initWithFrame_(self.frame())
        self.setContentView_(mainView)
        
        # 创建滚动视图
        scrollView = NSScrollView.alloc().initWithFrame_(mainView.frame())
        scrollView.setHasVerticalScroller_(True)
        scrollView.setHasHorizontalScroller_(False)
        scrollView.setAutoresizingMask_(NSViewWidthSizable | NSViewHeightSizable)
        
        # 创建文本视图
        self.textView = NSTextView.alloc().initWithFrame_(scrollView.frame())
        self.textView.setEditable_(False)
        self.textView.setSelectable_(True)
        self.textView.setAutoresizingMask_(NSViewWidthSizable | NSViewHeightSizable)
        
        # 设置文本视图样式
        self.textView.setFont_(NSFont.systemFontOfSize_(13.0))
        self.textView.setTextColor_(NSColor.textColor())
        
        # 将文本视图添加到滚动视图
        scrollView.setDocumentView_(self.textView)
        
        # 将滚动视图添加到主视图
        mainView.addSubview_(scrollView)
        
        # 添加控制按钮
        buttonFrame = ((10, 10), (60, 30))
        
        # 关闭按钮
        closeButton = NSButton.alloc().initWithFrame_(buttonFrame)
        closeButton.setTitle_("关闭")
        closeButton.setBezelStyle_(NSRoundedBezelStyle)
        closeButton.setAction_("close:")
        closeButton.setTarget_(self)
        mainView.addSubview_(closeButton)
        
        # 复制按钮
        copyButton = NSButton.alloc().initWithFrame_(((80, 10), (60, 30)))
        copyButton.setTitle_("复制")
        copyButton.setBezelStyle_(NSRoundedBezelStyle)
        copyButton.setAction_("copyTranslation:")
        copyButton.setTarget_(self)
        mainView.addSubview_(copyButton)
    
    def setTranslationText_(self, text):
        self.textView.setString_(text)
    
    def showAtPosition_(self, point):
        screen = NSScreen.mainScreen()
        frame = screen.visibleFrame()
        
        # 确保窗口不会超出屏幕
        x = min(max(point.x, frame.origin.x), 
                frame.origin.x + frame.size.width - self.frame().size.width)
        y = min(max(point.y, frame.origin.y), 
                frame.origin.y + frame.size.height - self.frame().size.height)
        
        self.setFrameOrigin_((x, y))
        self.makeKeyAndOrderFront_(None)
    
    def copyTranslation_(self, sender):
        text = self.textView.string()
        pyperclip.copy(text)
        # 显示复制成功提示
        NSBeep()

class TranslatorApp(NSObject):
    _sharedTranslator = None
    
    @classmethod
    def sharedTranslator(cls):
        if cls._sharedTranslator is None:
            cls._sharedTranslator = cls.alloc().init()
        return cls._sharedTranslator
    
    def init(self):
        print("初始化应用程序...")
        self = objc.super(TranslatorApp, self).init()
        if self is None: return None
        
        self.window = None
        self.last_selection = ""
        self.last_selection_time = 0
        self.selection_thread = None
        self.is_running = True
        
        try:
            # 创建状态栏图标
            print("创建状态栏图标...")
            self.statusbar = NSStatusBar.systemStatusBar()
            print("状态栏对象创建成功")
            
            # 使用固定长度而不是可变长度
            self.statusItem = self.statusbar.statusItemWithLength_(22)
            print("状态栏项目创建成功")
            
            # 优先加载自定义图标
            icon_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'resources', 'icon.png')
            if os.path.exists(icon_path):
                image = NSImage.alloc().initByReferencingFile_(icon_path)
                image.setSize_((18, 18))
                image.setTemplate_(True)
                print(f"已加载自定义图标: {icon_path}")
            else:
                image = NSImage.imageNamed_("NSStatusAvailable")
                if image is None:
                    # 如果系统图标不可用，创建一个自定义蓝色圆形图标
                    image = NSImage.alloc().initWithSize_((18, 18))
                    image.setTemplate_(True)
                    image.lockFocus()
                    path = NSBezierPath.bezierPathWithOvalInRect_(((0, 0), (18, 18)))
                    NSColor.systemBlueColor().set()
                    path.fill()
                    image.unlockFocus()
                print("使用系统图标或蓝色圆形图标")
            self.statusItem.setImage_(image)
            print("状态栏图标设置成功")
            
            # 只显示文字，不用图标（直接设置title，不要setImage_）
            self.statusItem.setImage_(None)
            self.statusItem.button().setTitle_("译")
            print("状态栏文字设置成功")
            
            # 创建菜单
            print("创建菜单...")
            self.menu = NSMenu.alloc().init()
            print("菜单创建成功")
            
            # 添加翻译菜单项
            translateItem = NSMenuItem.alloc().init()
            translateItem.setTitle_("翻译选中文本")
            translateItem.setAction_("translateSelectedText")
            translateItem.setTarget_(self)
            translateItem.setKeyEquivalent_("t")
            translateItem.setKeyEquivalentModifierMask_(NSCommandKeyMask)
            self.menu.addItem_(translateItem)
            print("翻译菜单项添加成功")
            
            # 添加分隔线
            self.menu.addItem_(NSMenuItem.separatorItem())
            print("分隔线添加成功")
            
            # 添加退出菜单项
            quitItem = NSMenuItem.alloc().init()
            quitItem.setTitle_("退出")
            quitItem.setAction_("terminate:")
            quitItem.setTarget_(NSApp)
            quitItem.setKeyEquivalent_("q")
            quitItem.setKeyEquivalentModifierMask_(NSCommandKeyMask)
            self.menu.addItem_(quitItem)
            print("退出菜单项添加成功")
            
            # 设置菜单
            self.statusItem.setMenu_(self.menu)
            print("菜单设置成功")
            
        except Exception as e:
            print(f"状态栏创建过程中出错: {str(e)}")
            print(traceback.format_exc())
        
        # 启动选择监听线程
        self.startSelectionMonitor()
        
        print("初始化完成")
        return self
    
    def applicationDidFinishLaunching_(self, notification):
        print("应用程序已启动")
        try:
            # 确保应用程序保持运行
            NSApp.setActivationPolicy_(NSApplicationActivationPolicyAccessory)
            print("应用程序激活策略设置成功")
            NSApp.activateIgnoringOtherApps_(True)
            print("应用程序激活成功")
        except Exception as e:
            print(f"应用程序启动过程中出错: {str(e)}")
            print(traceback.format_exc())
    
    def applicationWillTerminate_(self, notification):
        print("应用程序即将退出")
        self.is_running = False
        if self.selection_thread:
            self.selection_thread.join(timeout=1.0)
    
    def translateText_(self, text):
        if not text:
            return
        
        # 调用vllm API进行翻译
        try:
            # 构建API请求
            headers = {
                "Content-Type": "application/json"
            }
            
            data = {
                "model": "Qwen/Qwen-7B-Chat",
                "messages": [
                    {
                        "role": "user",
                        "content": f"请将以下文本翻译成中文：\n{text}"
                    }
                ],
                "temperature": 0.7,
                "max_tokens": 1000
            }
            
            # 发送请求
            response = requests.post(
                f"{VLLM_API_URL}/chat/completions",
                headers=headers,
                json=data
            )
            
            # 解析响应
            if response.status_code == 200:
                result = response.json()
                translation = result['choices'][0]['message']['content'].strip()
            else:
                translation = f"翻译出错: HTTP {response.status_code}"
                
        except Exception as e:
            translation = f"翻译出错: {str(e)}"
        
        # 显示翻译结果
        window = self.getWindow()
        window.setTranslationText_(f"原文:\n{text}\n\n翻译:\n{translation}")
        
        # 获取鼠标位置并显示窗口
        mouse_loc = NSEvent.mouseLocation()
        window.showAtPosition_(mouse_loc)
    
    def translateSelectedText(self):
        # 获取选中的文本
        selected_text = self.last_selection
        if not selected_text:
            return
        
        self.translateText_(selected_text)
