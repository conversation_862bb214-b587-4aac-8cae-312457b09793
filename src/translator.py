#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from Foundation import *
from AppKit import *
from PyObjCTools import AppHelper
import subprocess
import pyperclip
import json
import os
import requests
import objc
import time
import threading
import traceback
from Quartz import *

VLLM_API_URL = "http://*************:8000/v1"

# 添加辅助功能权限检查
def check_accessibility_permissions():
    """检查是否有辅助功能权限"""
    try:
        from Quartz.CoreGraphics import CGEventCreateKeyboardEvent
        # 尝试创建一个测试事件来检查权限
        test_event = CGEventCreateKeyboardEvent(None, 0, True)
        return test_event is not None
    except Exception:
        return False

# 移除 TranslationService 类，直接在 TranslatorApp 中处理

class MyTranslationPanel(NSPanel):
    def init(self):
        self = objc.super(MyTranslationPanel, self).init()
        if self is None: return None

        # 设置窗口样式
        self.setStyleMask_(NSTitledWindowMask | NSClosableWindowMask | NSResizableWindowMask)
        self.setFloatingPanel_(True)
        self.setBecomesKeyOnlyIfNeeded_(True)
        self.setLevel_(NSFloatingWindowLevel)

        # 设置窗口大小和位置
        self.setFrame_(((0, 0), (400, 300)))

        # 创建UI
        self.setupUI()

        return self

    def setupUI(self):
        # 创建主视图
        mainView = NSView.alloc().initWithFrame_(self.frame())
        self.setContentView_(mainView)

        # 创建滚动视图
        scrollView = NSScrollView.alloc().initWithFrame_(mainView.frame())
        scrollView.setHasVerticalScroller_(True)
        scrollView.setHasHorizontalScroller_(False)
        scrollView.setAutoresizingMask_(NSViewWidthSizable | NSViewHeightSizable)

        # 创建文本视图
        self.textView = NSTextView.alloc().initWithFrame_(scrollView.frame())
        self.textView.setEditable_(False)
        self.textView.setSelectable_(True)
        self.textView.setAutoresizingMask_(NSViewWidthSizable | NSViewHeightSizable)

        # 设置文本视图样式
        self.textView.setFont_(NSFont.systemFontOfSize_(13.0))
        self.textView.setTextColor_(NSColor.textColor())

        # 将文本视图添加到滚动视图
        scrollView.setDocumentView_(self.textView)

        # 将滚动视图添加到主视图
        mainView.addSubview_(scrollView)

        # 添加控制按钮
        buttonFrame = ((10, 10), (60, 30))

        # 关闭按钮
        closeButton = NSButton.alloc().initWithFrame_(buttonFrame)
        closeButton.setTitle_("关闭")
        closeButton.setBezelStyle_(NSRoundedBezelStyle)
        closeButton.setAction_("close:")
        closeButton.setTarget_(self)
        mainView.addSubview_(closeButton)

        # 复制按钮
        copyButton = NSButton.alloc().initWithFrame_(((80, 10), (60, 30)))
        copyButton.setTitle_("复制")
        copyButton.setBezelStyle_(NSRoundedBezelStyle)
        copyButton.setAction_("copyTranslation:")
        copyButton.setTarget_(self)
        mainView.addSubview_(copyButton)

    def setTranslationText_(self, text):
        self.textView.setString_(text)

    def showAtPosition_(self, point):
        screen = NSScreen.mainScreen()
        frame = screen.visibleFrame()

        # 确保窗口不会超出屏幕
        x = min(max(point.x, frame.origin.x),
                frame.origin.x + frame.size.width - self.frame().size.width)
        y = min(max(point.y, frame.origin.y),
                frame.origin.y + frame.size.height - self.frame().size.height)

        self.setFrameOrigin_((x, y))
        self.makeKeyAndOrderFront_(None)

    def copyTranslation_(self, sender):
        text = self.textView.string()
        pyperclip.copy(text)
        # 显示复制成功提示
        NSBeep()

class MyTranslatorApp(NSObject):
    _sharedTranslator = None

    @classmethod
    def sharedTranslator(cls):
        if cls._sharedTranslator is None:
            cls._sharedTranslator = cls.alloc().init()
        return cls._sharedTranslator

    def init(self):
        print("初始化应用程序...")
        self = objc.super(MyTranslatorApp, self).init()
        if self is None: return None

        self.window = None
        self.last_selection = ""
        self.last_selection_time = 0
        self.selection_thread = None
        self.is_running = True
        self.loading_window = None

        try:
            # 创建状态栏图标
            print("创建状态栏图标...")
            self.statusbar = NSStatusBar.systemStatusBar()
            print("状态栏对象创建成功")

            # 使用固定长度而不是可变长度
            self.statusItem = self.statusbar.statusItemWithLength_(22)
            print("状态栏项目创建成功")

            # 优先加载自定义图标
            icon_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'resources', 'icon.png')
            if os.path.exists(icon_path):
                image = NSImage.alloc().initByReferencingFile_(icon_path)
                image.setSize_((18, 18))
                image.setTemplate_(True)
                print(f"已加载自定义图标: {icon_path}")
            else:
                image = NSImage.imageNamed_("NSStatusAvailable")
                if image is None:
                    # 如果系统图标不可用，创建一个自定义蓝色圆形图标
                    image = NSImage.alloc().initWithSize_((18, 18))
                    image.setTemplate_(True)
                    image.lockFocus()
                    path = NSBezierPath.bezierPathWithOvalInRect_(((0, 0), (18, 18)))
                    NSColor.systemBlueColor().set()
                    path.fill()
                    image.unlockFocus()
                print("使用系统图标或蓝色圆形图标")
            self.statusItem.setImage_(image)
            print("状态栏图标设置成功")

            # 只显示文字，不用图标（直接设置title，不要setImage_）
            self.statusItem.setImage_(None)
            self.statusItem.button().setTitle_("译")
            print("状态栏文字设置成功")

            # 创建菜单
            print("创建菜单...")
            self.menu = NSMenu.alloc().init()
            print("菜单创建成功")

            # 添加翻译菜单项
            translateItem = NSMenuItem.alloc().init()
            translateItem.setTitle_("翻译选中文本")
            translateItem.setAction_("translateSelectedText")
            translateItem.setTarget_(self)
            translateItem.setKeyEquivalent_("t")
            translateItem.setKeyEquivalentModifierMask_(NSCommandKeyMask)
            self.menu.addItem_(translateItem)
            print("翻译菜单项添加成功")

            # 添加注释菜单项
            commentItem = NSMenuItem.alloc().init()
            commentItem.setTitle_("注释选中代码")
            commentItem.setAction_("commentSelectedText")
            commentItem.setTarget_(self)
            commentItem.setKeyEquivalent_("c")
            commentItem.setKeyEquivalentModifierMask_(NSCommandKeyMask)
            self.menu.addItem_(commentItem)
            print("注释菜单项添加成功")

            # 添加解释菜单项
            explainItem = NSMenuItem.alloc().init()
            explainItem.setTitle_("解释选中内容")
            explainItem.setAction_("explainSelectedText")
            explainItem.setTarget_(self)
            explainItem.setKeyEquivalent_("e")
            explainItem.setKeyEquivalentModifierMask_(NSCommandKeyMask)
            self.menu.addItem_(explainItem)
            print("解释菜单项添加成功")

            # 添加分隔线
            self.menu.addItem_(NSMenuItem.separatorItem())
            print("分隔线添加成功")

            # 添加设置菜单项
            settingsItem = NSMenuItem.alloc().init()
            settingsItem.setTitle_("设置")
            settingsItem.setAction_("showSettings")
            settingsItem.setTarget_(self)
            self.menu.addItem_(settingsItem)
            print("设置菜单项添加成功")

            # 添加退出菜单项
            quitItem = NSMenuItem.alloc().init()
            quitItem.setTitle_("退出")
            quitItem.setAction_("terminate:")
            quitItem.setTarget_(NSApp)
            quitItem.setKeyEquivalent_("q")
            quitItem.setKeyEquivalentModifierMask_(NSCommandKeyMask)
            self.menu.addItem_(quitItem)
            print("退出菜单项添加成功")

            # 设置菜单
            self.statusItem.setMenu_(self.menu)
            print("菜单设置成功")

        except Exception as e:
            print(f"状态栏创建过程中出错: {str(e)}")
            print(traceback.format_exc())

        # 检查辅助功能权限
        if not check_accessibility_permissions():
            self.showAccessibilityAlert()

        print("初始化完成")
        return self

    def applicationDidFinishLaunching_(self, notification):
        print("应用程序已启动")
        try:
            # 确保应用程序保持运行
            NSApp.setActivationPolicy_(NSApplicationActivationPolicyAccessory)
            print("应用程序激活策略设置成功")
            NSApp.activateIgnoringOtherApps_(True)
            print("应用程序激活成功")
        except Exception as e:
            print(f"应用程序启动过程中出错: {str(e)}")
            print(traceback.format_exc())

    def applicationWillTerminate_(self, notification):
        print("应用程序即将退出")
        self.is_running = False
        if self.selection_thread:
            self.selection_thread.join(timeout=1.0)

    def showAccessibilityAlert(self):
        """显示辅助功能权限提醒"""
        alert = NSAlert.alloc().init()
        alert.setMessageText_("需要辅助功能权限")
        alert.setInformativeText_("为了获取选中的文本，请在系统偏好设置 > 安全性与隐私 > 隐私 > 辅助功能中添加此应用程序。")
        alert.setAlertStyle_(NSWarningAlertStyle)
        alert.addButtonWithTitle_("打开系统偏好设置")
        alert.addButtonWithTitle_("稍后设置")

        response = alert.runModal()
        if response == NSAlertFirstButtonReturn:
            # 打开系统偏好设置
            subprocess.call(["open", "x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility"])

    def getSelectedText(self):
        """获取当前选中的文本"""
        try:
            # 方法1: 尝试从剪贴板获取（需要用户先复制）
            # 保存当前剪贴板内容
            original_clipboard = pyperclip.paste()

            # 模拟 Cmd+C 复制选中文本
            self.simulateKeyPress_withModifiers_('c', NSCommandKeyMask)
            time.sleep(0.1)  # 等待复制完成

            # 获取新的剪贴板内容
            selected_text = pyperclip.paste()

            # 如果内容没有变化，说明没有选中文本
            if selected_text == original_clipboard:
                return None

            return selected_text

        except Exception as e:
            print(f"获取选中文本失败: {str(e)}")
            return None

    def simulateKeyPress_withModifiers_(self, key, modifiers):
        """模拟按键"""
        try:
            from Quartz.CoreGraphics import CGEventCreateKeyboardEvent, CGEventSetFlags, CGEventPost, kCGHIDEventTap

            # 键码映射
            key_codes = {
                'c': 8,  # C 键
                'v': 9,  # V 键
                'x': 7,  # X 键
                'a': 0,  # A 键
            }

            key_code = key_codes.get(key.lower(), 8)  # 默认使用 C 键

            # 创建按键事件
            event = CGEventCreateKeyboardEvent(None, key_code, True)
            CGEventSetFlags(event, modifiers)
            CGEventPost(kCGHIDEventTap, event)

            # 释放按键
            event = CGEventCreateKeyboardEvent(None, key_code, False)
            CGEventPost(kCGHIDEventTap, event)
        except Exception as e:
            print(f"模拟按键失败: {str(e)}")

    def showLoadingIndicator(self, message="处理中..."):
        """显示加载指示器"""
        if self.loading_window:
            self.loading_window.close()

        self.loading_window = NSPanel.alloc().init()
        self.loading_window.setStyleMask_(NSTitledWindowMask)
        self.loading_window.setFloatingPanel_(True)
        self.loading_window.setLevel_(NSFloatingWindowLevel)
        self.loading_window.setFrame_(((0, 0), (200, 60)))

        # 创建标签
        label = NSTextField.alloc().initWithFrame_(((10, 20), (180, 20)))
        label.setStringValue_(message)
        label.setEditable_(False)
        label.setBordered_(False)
        label.setBackgroundColor_(NSColor.clearColor())
        label.setAlignment_(NSCenterTextAlignment)

        self.loading_window.contentView().addSubview_(label)

        # 居中显示
        screen = NSScreen.mainScreen()
        frame = screen.visibleFrame()
        x = frame.origin.x + (frame.size.width - 200) / 2
        y = frame.origin.y + (frame.size.height - 60) / 2
        self.loading_window.setFrameOrigin_((x, y))
        self.loading_window.makeKeyAndOrderFront_(None)

    def hideLoadingIndicator(self):
        """隐藏加载指示器"""
        if self.loading_window:
            self.loading_window.close()
            self.loading_window = None

    def callVLLMAPI_withTaskType_(self, prompt, task_type):
        """调用 vLLM API"""
        try:
            headers = {
                "Content-Type": "application/json"
            }

            data = {
                "model": "Qwen/Qwen-7B-Chat",
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.7,
                "max_tokens": 1000
            }

            response = requests.post(
                f"{VLLM_API_URL}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content'].strip()
            else:
                return f"{task_type}出错: HTTP {response.status_code}"

        except Exception as e:
            return f"{task_type}出错: {str(e)}"

    def translateText_(self, text):
        """翻译文本"""
        if not text:
            return

        def translate_async():
            self.showLoadingIndicator("翻译中...")

            prompt = f"请将以下文本翻译成中文，如果原文是中文则翻译成英文：\n{text}"
            translation = self.callVLLMAPI_withTaskType_(prompt, "翻译")

            def show_result():
                self.hideLoadingIndicator()
                window = self.getWindow()
                window.setTranslationText_(f"原文:\n{text}\n\n翻译:\n{translation}")

                mouse_loc = NSEvent.mouseLocation()
                window.showAtPosition_(mouse_loc)

            # 在主线程中显示结果
            NSOperationQueue.mainQueue().addOperationWithBlock_(show_result)

        # 在后台线程中执行翻译
        threading.Thread(target=translate_async, daemon=True).start()

    def translateSelectedText(self):
        """翻译选中的文本"""
        selected_text = self.getSelectedText()
        if not selected_text:
            self.showAlert_withMessage_("提示", "请先选中要翻译的文本")
            return

        self.translateText_(selected_text)

    def commentSelectedText(self):
        """为选中的代码添加注释"""
        selected_text = self.getSelectedText()
        if not selected_text:
            self.showAlert_withMessage_("提示", "请先选中要注释的代码")
            return

        def comment_async():
            self.showLoadingIndicator("生成注释中...")

            prompt = f"请为以下代码添加详细的中文注释，保持原有代码结构不变：\n\n{selected_text}"
            commented_code = self.callVLLMAPI_withTaskType_(prompt, "注释")

            def show_result():
                self.hideLoadingIndicator()
                window = self.getWindow()
                window.setTranslationText_(f"原代码:\n{selected_text}\n\n注释后:\n{commented_code}")

                mouse_loc = NSEvent.mouseLocation()
                window.showAtPosition_(mouse_loc)

            NSOperationQueue.mainQueue().addOperationWithBlock_(show_result)

        threading.Thread(target=comment_async, daemon=True).start()

    def explainSelectedText(self):
        """解释选中的内容"""
        selected_text = self.getSelectedText()
        if not selected_text:
            self.showAlert("提示", "请先选中要解释的内容")
            return

        def explain_async():
            self.showLoadingIndicator("解释中...")

            prompt = f"请详细解释以下内容的含义和作用：\n\n{selected_text}"
            explanation = self.callVLLMAPI_withTaskType_(prompt, "解释")

            def show_result():
                self.hideLoadingIndicator()
                window = self.getWindow()
                window.setTranslationText_(f"原内容:\n{selected_text}\n\n解释:\n{explanation}")

                mouse_loc = NSEvent.mouseLocation()
                window.showAtPosition_(mouse_loc)

            NSOperationQueue.mainQueue().addOperationWithBlock_(show_result)

        threading.Thread(target=explain_async, daemon=True).start()

    def showSettings(self):
        """显示设置窗口"""
        alert = NSAlert.alloc().init()
        alert.setMessageText_("设置")
        alert.setInformativeText_(f"当前 vLLM API 地址: {VLLM_API_URL}\n\n功能说明:\n• 翻译: 中英文互译\n• 注释: 为代码添加中文注释\n• 解释: 解释选中内容的含义")
        alert.setAlertStyle_(NSInformationalAlertStyle)
        alert.addButtonWithTitle_("确定")
        alert.runModal()

    def showAlert_withMessage_(self, title, message):
        """显示提示框"""
        alert = NSAlert.alloc().init()
        alert.setMessageText_(title)
        alert.setInformativeText_(message)
        alert.setAlertStyle_(NSInformationalAlertStyle)
        alert.addButtonWithTitle_("确定")
        alert.runModal()

    def getWindow(self):
        """获取或创建翻译窗口"""
        if self.window is None:
            self.window = MyTranslationPanel.alloc().init()
            self.window.setTitle_("翻译结果")
        return self.window


def main():
    """主程序入口"""
    print("启动翻译工具...")

    # 创建应用程序
    app = NSApplication.sharedApplication()

    # 创建翻译器实例
    translator = MyTranslatorApp.sharedTranslator()

    # 设置应用程序代理
    app.setDelegate_(translator)

    try:
        print("开始运行应用程序...")
        # 运行应用程序
        AppHelper.runEventLoop()
    except KeyboardInterrupt:
        print("收到中断信号，正在退出...")
        app.terminate_(None)
    except Exception as e:
        print(f"应用程序运行出错: {str(e)}")
        print(traceback.format_exc())


if __name__ == "__main__":
    main()
