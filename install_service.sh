#!/bin/bash

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# 创建服务目录
SERVICE_DIR="$HOME/Library/Services/Translator.service"
mkdir -p "$SERVICE_DIR/Contents/MacOS"

# 复制服务文件
cp "$SCRIPT_DIR/Translator.service/Contents/Info.plist" "$SERVICE_DIR/Contents/"

# 复制可执行文件
cp "$SCRIPT_DIR/src/translator.py" "$SERVICE_DIR/Contents/MacOS/"

# 设置权限
chmod +x "$SERVICE_DIR/Contents/MacOS/translator.py"

# 刷新服务
/System/Library/CoreServices/pbs -flush

echo "服务安装完成！请重启应用程序以使更改生效。" 