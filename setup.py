from setuptools import setup

APP = ['src/translator.py']
DATA_FILES = []
OPTIONS = {
    'argv_emulation': True,
    'packages': ['Foundation', 'AppKit', 'objc'],
    'plist': {
        'LSUIElement': 1,
        'CFBundleName': 'Translator',
        'CFBundleDisplayName': 'Translator',
        'CFBundleGetInfoString': "翻译工具",
        'CFBundleIdentifier': "com.translator.app",
        'CFBundleVersion': "0.1.0",
        'CFBundleShortVersionString': "0.1.0",
        'NSHumanReadableCopyright': u"Copyright © 2024, All Rights Reserved"
    },
    'iconfile': 'icon.icns',
}

setup(
    app=APP,
    data_files=DATA_FILES,
    options={'py2app': OPTIONS},
    setup_requires=['py2app'],
) 