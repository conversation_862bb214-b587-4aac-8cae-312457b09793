# macOS 翻译工具

一个简单的 macOS 翻译工具，可以通过选中文本并点击状态栏图标来触发翻译。

## 功能特点

- 支持选中文本翻译
- 悬浮窗口显示翻译结果
- 状态栏快速访问
- 支持快捷键操作

## 安装要求

- Python 3.7+
- macOS 10.15+

## 安装步骤

1. 克隆仓库：
```bash
git clone [repository_url]
cd TranslatorTool
```

2. 安装依赖：
```bash
pip install -r requirements.txt
```

3. 运行程序：
```bash
python src/translator.py
```

## 使用方法

1. 在任意应用中选中要翻译的文本
2. 点击状态栏的"译"图标
3. 选择"翻译选中文本"选项
4. 查看翻译结果

## 快捷键

- Command + T: 翻译选中文本
- Command + Q: 退出程序

## 注意事项

- 需要确保 vllm 已正确安装并配置
- 程序需要访问辅助功能权限以监听文本选择

## 许可证

MIT License 