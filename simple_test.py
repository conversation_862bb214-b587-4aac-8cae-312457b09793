#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的翻译工具测试脚本
"""

import sys
import os

# 添加 src 目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_basic_imports():
    """测试基本导入"""
    print("测试基本导入...")
    try:
        from Foundation import NSObject
        from AppKit import NSApplication, NSStatusBar, NSMenu, NSMenuItem
        import pyperclip
        import requests
        print("✅ 基本导入成功")
        return True
    except Exception as e:
        print(f"❌ 基本导入失败: {str(e)}")
        return False

def test_translator_import():
    """测试翻译器导入"""
    print("测试翻译器导入...")
    try:
        from translator import MyTranslatorApp, check_accessibility_permissions
        print("✅ 翻译器导入成功")
        return True
    except Exception as e:
        print(f"❌ 翻译器导入失败: {str(e)}")
        return False

def test_app_creation():
    """测试应用创建"""
    print("测试应用创建...")
    try:
        from translator import MyTranslatorApp
        app = MyTranslatorApp.alloc().init()
        print("✅ 应用创建成功")
        return True
    except Exception as e:
        print(f"❌ 应用创建失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("简化测试开始")
    print("="*30)

    tests = [
        ("基本导入", test_basic_imports),
        ("翻译器导入", test_translator_import),
        ("应用创建", test_app_creation),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1

    print("\n" + "="*30)
    print(f"测试结果: {passed}/{total} 通过")

    if passed == total:
        print("✅ 基本测试通过！可以尝试运行应用程序")
        print("运行命令: python3 src/translator.py")
    else:
        print("❌ 基本测试失败")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
