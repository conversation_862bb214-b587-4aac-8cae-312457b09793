#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版翻译工具测试
"""

import sys
import os
import requests

# 添加 src 目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

VLLM_API_URL = "http://10.164.110.54:8000/v1"

def test_vllm_api():
    """测试 vLLM API 连接"""
    print("测试 vLLM API 连接...")
    try:
        headers = {"Content-Type": "application/json"}
        data = {
            "model": "qwen",
            "messages": [
                {
                    "role": "user",
                    "content": "Hello, please translate this to Chinese."
                }
            ],
            "temperature": 0.7,
            "max_tokens": 100
        }

        response = requests.post(
            f"{VLLM_API_URL}/chat/completions",
            headers=headers,
            json=data,
            timeout=10
        )

        if response.status_code == 200:
            result = response.json()
            print("✅ vLLM API 连接成功")
            print(f"响应: {result['choices'][0]['message']['content']}")
            return True
        else:
            print(f"❌ vLLM API 连接失败: HTTP {response.status_code}")
            print(f"响应内容: {response.text}")
            return False

    except Exception as e:
        print(f"❌ vLLM API 连接出错: {str(e)}")
        return False

def test_imports():
    """测试导入"""
    print("测试导入...")
    try:
        from simple_translator import SimpleTranslatorApp
        print("✅ 简化翻译器导入成功")
        return True
    except Exception as e:
        print(f"❌ 简化翻译器导入失败: {str(e)}")
        return False

def test_app_creation():
    """测试应用创建"""
    print("测试应用创建...")
    try:
        from simple_translator import SimpleTranslatorApp
        app = SimpleTranslatorApp.alloc().init()
        print("✅ 应用创建成功")
        return True
    except Exception as e:
        print(f"❌ 应用创建失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("简化版翻译工具测试")
    print("="*40)

    tests = [
        ("导入测试", test_imports),
        ("应用创建", test_app_creation),
        ("vLLM API 连接", test_vllm_api),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1

    print("\n" + "="*40)
    print(f"测试结果: {passed}/{total} 通过")

    if passed >= 2:  # 至少导入和创建成功
        print("✅ 基本功能测试通过！")
        print("\n使用说明:")
        print("1. 运行: python3 src/simple_translator.py")
        print("2. 在状态栏找到 '译' 图标")
        print("3. 复制要翻译的文本到剪贴板")
        print("4. 点击菜单选择功能")
        print("5. 结果会自动复制到剪贴板")
    else:
        print("❌ 基本功能测试失败")

    return passed >= 2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
